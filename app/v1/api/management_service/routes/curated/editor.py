from fastapi import API<PERSON>out<PERSON>, Depends, HTTPException, BackgroundTasks
from typing import Dict, Any
from pymongo.errors import PyMongoError
from app.shared.models.user import UserTenantDB
from app.shared.security import get_tenant_info
from app.shared.utils.logger import setup_new_logging
from app.shared.db_enums import Collection<PERSON>ame
from app.shared.utils.mongo_helper import serialize_mongo_doc
from app.v1.api.management_service.creator import save_to_database
loggers = setup_new_logging(__name__)


router = APIRouter( 
    tags=["Curated Editor"],
    responses={404: {"description": "Not found"}}
)

from app.shared.models.curated_content import CuratedContentRequest, CuratedContentResponse

@router.post("/generate", response_model=CuratedContentResponse)
async def generate_curated_content(
    request: CuratedContentRequest,
    background_tasks: BackgroundTasks,
    current_user: UserTenantDB = Depends(get_tenant_info),
):
    """
    Generate curated content based on the provided input.

    This endpoint generates curated content including tasks and stories,
    saves them to the database, and starts background media generation.

    Args:
        request: CuratedContentRequest containing the story prompt
        current_user: Current authenticated user

    Returns:
        CuratedContentResponse with generation results and metadata
    """
    try:
        loggers.info(f"Starting curated content generation for user {current_user.user.id}")

        # Call the save_to_database function directly to get the result
        result = await save_to_database(request, current_user,background_tasks)

        # Return the result which already matches CuratedContentResponse format
        return result

    except Exception as e:
        loggers.error(f"Error generating curated content: {e}")
        raise HTTPException(status_code=500, detail="Failed to generate curated content")


@router.post("/get_prompts", response_model=Dict[str, Any])
async def get_prompts(
    current_user: UserTenantDB = Depends(get_tenant_info),
):
    """
    Retrieve prompts for the current user.
    
    Args:
        current_user (UserTenantDB): The current user information.
    
    Returns:
        Dict[str, Any]: A dictionary containing the prompts.
    """
    try:
        # Simulate fetching prompts from the database
        prompts = await current_user.async_db[CollectionName.editor_prompts].find(
            {"user_id": current_user.user.id}
        ).to_list(length=None)
        
        return {"data":serialize_mongo_doc(prompts)}
    except PyMongoError as e:
        loggers.error(f"Database error: {e}")
        raise HTTPException(status_code=500, detail="Database error occurred")

