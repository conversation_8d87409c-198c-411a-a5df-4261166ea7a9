from datetime import datetime, timezone
from bson import ObjectId
from pymongo.errors import PyMongoError
from fastapi import HTTPException, BackgroundTasks, Depends
from typing import Dict, Any
from app.shared.models.user import UserTenantDB
from app.shared.db_enums import Collection<PERSON>ame
from app.shared.utils.logger import setup_new_logging
from app.v1.api.management_service.routes.curated.editor import CuratedContentRequest
from app.v1.api.management_service.generators import(
    generate_content,
    generate_audio,
    generate_tasks,
    generate_image,
    generate_audio_task,
)

loggers = setup_new_logging(__name__)

async def save_to_database(
    request:CuratedContentRequest,
    current_user: UserTenantDB
):
    """
    Save generated content to the database.
    
    Args:
        content (Dict[str, Any]): The content to be saved.
        collection_name (str): The name of the collection to save to.
        current_user (UserTenantDB): The current user information.
    
    """
    try:
        # Simulate saving to database logic
        loggers.info(f"Saving content {request.content} for user {current_user.user.id}")

        await current_user.async_db[CollectionName.editor_prompts].insert_one({
            "content": request.content,
            "user_id": current_user.user.id,
            "task_set_id": ObjectId(),  # Assuming a new task set ID is generated
            "created_at":datetime.now(timezone.utc),
            "status": "pending"
        })

        # ste
        # Here you would typically use a database client to insert the content
    except PyMongoError as e:
        loggers.error(f"Database error: {e}")
        raise HTTPException(status_code=500, detail="Database error occurred")