from datetime import datetime, timezone
from bson import ObjectId
from pymongo.errors import PyMongoError
from fastapi import HTTPException
from app.shared.models.user import UserTenantDB
from app.shared.db_enums import CollectionName
from app.shared.utils.logger import setup_new_logging
from app.shared.models.curated_content import CuratedContentRequest
from app.v1.api.management_service.generators import (
    generate_content,
    generate_audio,
    generate_tasks,
    generate_image,
    generate_audio_task,
)
from app.v1.api.management_service.creator.split_and_manage import (
    split_and_manage,
    save_task_set_to_db,
    save_task_items_to_db,
    save_story_items_to_db,
    generate_media_for_tasks_and_stories
)

loggers = setup_new_logging(__name__)

async def save_to_database(
    request: CuratedContentRequest,
    current_user: UserTenantDB
):
    """
    Save generated content to the database.
    
    Args:
        content (Dict[str, Any]): The content to be saved.
        collection_name (str): The name of the collection to save to.
        current_user (UserTenantDB): The current user information.
    
    """
    try:
        # Simulate saving to database logic
        loggers.info(f"Saving content {request.content} for user {current_user.user.id}")
        task_set_id = ObjectId()

        await current_user.async_db[CollectionName.editor_prompts].insert_one({
            "content": request.content,
            "user_id": current_user.user.id,
            "task_set_id":task_set_id,  # Assuming a new task set ID is generated
            "created_at":datetime.now(timezone.utc),
            "status": "pending"
        })

        # step1 generate script
        try:
            generated_script, _usage_metadata = await generate_content(request.content)
            loggers.info(f"Generated script: {generated_script}")
        except Exception as e:
            loggers.error(f"Error generating script: {e}")
            raise HTTPException(status_code=500, detail=f"Script generation failed: {str(e)}")

        # step2 generate audio
        try:
            generated_audio, _audio_usage_metadata = await generate_audio(generated_script)
            loggers.info(f"Generated audio bytes length: {len(generated_audio) if generated_audio else 0}")

            # Save generated audio to MinIO (following v2 pattern)
            audio_storage_info = None
            if generated_audio and current_user.minio:
                try:
                    from app.shared.async_minio_client import create_async_minio_client
                    import hashlib

                    async_minio_client = create_async_minio_client(current_user.minio)

                    # Create a unique filename for the curated content audio
                    content_hash = hashlib.sha256(request.content.encode()).hexdigest()[:10]
                    custom_filename = f"curated_{content_hash}_audio.wav"

                    audio_storage_info = await async_minio_client.save_file_async(
                        data=generated_audio,
                        user_id=current_user.user.id,
                        content_type="audio/wav",
                        folder="curated_audio",
                        file_extension=".wav",
                        custom_filename=custom_filename,
                        script=generated_script,  # Store script in metadata
                    )
                    loggers.info(f"📁 Curated audio stored in MinIO: {audio_storage_info.get('object_path')}")
                except Exception as e:
                    loggers.error(f"❌ Error storing curated audio in MinIO: {e}")
                    audio_storage_info = {}

        except Exception as e:
            loggers.error(f"Error generating audio: {e}")
            raise HTTPException(status_code=500, detail=f"Audio generation failed: {str(e)}")

        # step3 generate tasks
        try:
            generated_tasks, _tasks_usage_metadata = await generate_tasks(generated_audio)
            loggers.info(f"Generated tasks: {generated_tasks}")

            # Validate generated tasks structure
            if not isinstance(generated_tasks, dict):
                raise ValueError(f"Generated tasks should be a dict, got {type(generated_tasks)}")

            if "error" in generated_tasks:
                raise ValueError(f"Task generation error: {generated_tasks['error']}")

            if not generated_tasks.get("tasks"):
                raise ValueError("No tasks found in generated content")

        except Exception as e:
            loggers.error(f"Error generating tasks: {e}")
            raise HTTPException(status_code=500, detail=f"Task generation failed: {str(e)}")

        # step4 split properly make three coll wise datast task_sets, task_items, and story
        task_set, task_items, story_items = await split_and_manage(generated_tasks)
        loggers.info(f"Split complete: task_set with {len(task_items)} task items, {len(story_items)} story items")

        # step5 save to database following v2 pattern: insert first, then generate media

        # First: Save task items and story items to database WITHOUT media
        task_item_ids = await save_task_items_to_db(
            current_user, task_items, task_set_id
        )

        story_item_ids = await save_story_items_to_db(
            current_user, story_items, task_set_id
        )

        # Update task_set with the actual task and story IDs
        task_set["tasks"] = [str(task_id) for task_id in task_item_ids]
        task_set["stories"] = [str(story_id) for story_id in story_item_ids]
        task_set["total_score"] = sum(item["total_score"] for item in task_items)

        # Second: Save task set with references to task and story IDs
        await save_task_set_to_db(
            current_user, task_set, task_set_id,
            generated_script, generated_audio, request.content, audio_storage_info
        )

        # Third: Generate media for task items and story items in background
        # This follows v2 pattern where media is generated AFTER database insertion
        import asyncio
        asyncio.create_task(generate_media_for_tasks_and_stories(
            current_user, task_item_ids, story_item_ids,
            generate_image, generate_audio_task
        ))

        loggers.info(f"Saved to database: task_set_id={task_set_id}, task_items={len(task_item_ids)}, story_items={len(story_item_ids)}")
        loggers.info("Background media generation started")

        return {
            "status": "success",
            "message": "Curated content generated and saved successfully",
            "task_set_id": str(task_set_id),
            "task_count": len(task_item_ids),
            "story_count": len(story_item_ids),
            "text_tasks_ready": task_set["text_tasks_ready"],
            "media_tasks_pending": task_set["media_tasks_pending"],
            "metadata": {
                "script_length": len(generated_script),
                "audio_size": len(generated_audio),
                "total_tasks": len(generated_tasks.get("tasks", [])),
                "media_generation": "started_in_background"
            }
        }

    except PyMongoError as e:
        loggers.error(f"Database error: {e}")
        raise HTTPException(status_code=500, detail="Database error occurred")
    except Exception as e:
        loggers.error(f"Unexpected error in save_to_database: {e}")
        raise HTTPException(status_code=500, detail=f"Content generation failed: {str(e)}")