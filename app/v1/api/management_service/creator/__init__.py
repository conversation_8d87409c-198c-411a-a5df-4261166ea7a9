from datetime import datetime, timezone
from bson import ObjectId
from pymongo.errors import PyMongoError
from fastapi import HTTPEx<PERSON>, BackgroundTasks, Depends
from typing import Dict, Any
from app.shared.models.user import UserTenantDB
from app.shared.db_enums import CollectionName
from app.shared.utils.logger import setup_new_logging
from app.v1.api.management_service.routes.curated.editor import CuratedContentRequest
from app.v1.api.management_service.generators import (
    generate_content,
    generate_audio,
    generate_tasks,
    generate_image,
    generate_audio_task,
)
from app.v1.api.management_service.creator.split_and_manage import (
    split_and_manage,
    save_task_set_to_db,
    save_task_items_to_db,
    save_story_items_to_db
)

loggers = setup_new_logging(__name__)

async def save_to_database(
    request:CuratedContentRequest,
    content_set_id: ObjectId,
    current_user: UserTenantDB
):
    """
    Save generated content to the database.
    
    Args:
        content (Dict[str, Any]): The content to be saved.
        collection_name (str): The name of the collection to save to.
        current_user (UserTenantDB): The current user information.
    
    """
    try:
        # Simulate saving to database logic
        loggers.info(f"Saving content {request.content} for user {current_user.user.id}")

        await current_user.async_db[CollectionName.editor_prompts].insert_one({
            "content": request.content,
            "user_id": current_user.user.id,
            "task_set_id": ObjectId(),  # Assuming a new task set ID is generated
            "created_at":datetime.now(timezone.utc),
            "status": "pending"
        })

        # step1 generate script
        generated_script,usage_metadata = await generate_content(request.content)
        loggers.info(f"Generated script: {generated_script}")
        # step2 generate audio
        generated_audio, audio_usage_metadata = await generate_audio(generated_script)
        loggers.info(f"Generated audio: {generated_audio}")
        # step3 generate tasks
        generated_tasks, tasks_usage_metadata = await generate_tasks(generated_script)
        loggers.info(f"Generated tasks: {generated_tasks}")

        # step4 split properly make three coll wise datast task_sets, task_items, and story
        task_set, task_items, story_items = await split_and_manage(generated_tasks)
        loggers.info(f"Split complete: task_set with {task_set['tasks_count']} tasks")

        # step5 save to database

        # Save task set to curated_content_set collection
        await save_task_set_to_db(
            current_user, task_set, content_set_id,
            generated_script, generated_audio, request.content
        )
        loggers.info(f"Saved task set with ID: {content_set_id}")

        # Save task items with media generation (ordered by task type)
        task_item_ids = await save_task_items_to_db(
            current_user, task_items, content_set_id,
            generate_image, generate_audio_task
        )
        loggers.info(f"Saved {len(task_item_ids)} task items")

        # Save story items with media generation
        story_item_ids = await save_story_items_to_db(
            current_user, story_items, content_set_id,
            generate_image, generate_audio_task
        )
        loggers.info(f"Saved {len(story_item_ids)} story items")

        loggers.info(f"Curated content generation complete for user {current_user.user.id}")

    except PyMongoError as e:
        loggers.error(f"Database error: {e}")
        raise HTTPException(status_code=500, detail="Database error occurred")
    except Exception as e:
        loggers.error(f"Unexpected error in save_to_database: {e}")
        raise HTTPException(status_code=500, detail=f"Content generation failed: {str(e)}")