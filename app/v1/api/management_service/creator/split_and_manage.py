"""
Helper functions for splitting and managing curated content generation.

This module provides utilities to split generated tasks into proper database structures
and manage media generation for task items and story items.
"""

from datetime import datetime, timezone
from bson import ObjectId
from typing import Dict, Any, List, Tuple
import base64
from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from fastapi import HTTPException

loggers = setup_new_logging(__name__)


async def split_and_manage(generated_tasks: Dict[str, Any]) -> Tuple[Dict[str, Any], List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    Split generated tasks into task_set, task_items, and story items.

    Args:
        generated_tasks: Output from generate_tasks function

    Returns:
        Tuple of (task_set_dict, task_items_list, story_items_list)
    """
    try:
        loggers.info("Starting split_and_manage for curated content")

        # Extract basic information
        title = generated_tasks.get("title", "Generated Curated Content")
        tasks = generated_tasks.get("tasks", [])
        metadata = generated_tasks.get("metadata", {})

        if not tasks:
            raise ValueError("No tasks found in generated_tasks")

        # Create task set structure
        task_set = {
            "title": title,
            "total_tasks": len(tasks),
            "total_stories": len(tasks),  # Each task has a story
            "difficulty_level": 1,  # Default to easy
            "status": "pending",
            "gentype": "primary",
            "metadata": metadata,
            "tasks_count": len(tasks)
        }

        # Create task items and story items
        task_items = []
        story_items = []

        for i, task in enumerate(tasks):
            # Extract task data
            question_data = task.get("question", {})
            story_data = task.get("story", {})

            # Create task item
            task_item = {
                "task_index": i + 1,
                "question": {
                    "type": question_data.get("type", "single_choice"),
                    "text": question_data.get("text", ""),
                    "translated_text": question_data.get("translated_text", ""),
                    "options": question_data.get("options", {}),
                    "answer": question_data.get("answer", ""),
                    "answer_hint": question_data.get("answer_hint", ""),
                },
                "max_score": task.get("max_score", 10),
                "complexity": task.get("complexity", 1),
                "needs_image": question_data.get("type") in ["image_identification"],
                "needs_audio": question_data.get("type") in ["speak_word", "word_identification"],
                "media_generated": False,
                "media_files": {}
            }
            task_items.append(task_item)

            # Create story item
            story_item = {
                "story_index": i + 1,
                "stage": story_data.get("stage", f"Chapter {i + 1}"),
                "thumbnail": story_data.get("thumbnail", "📖"),
                "script": story_data.get("script", ""),
                "image_description": story_data.get("image", ""),
                "audio_keyword": story_data.get("audio", ""),
                "needs_image": bool(story_data.get("image", "")),
                "needs_audio": bool(story_data.get("audio", "")),
                "media_generated": False,
                "media_files": {}
            }
            story_items.append(story_item)

        loggers.info(f"Split complete: {len(task_items)} task items, {len(story_items)} story items")
        return task_set, task_items, story_items

    except Exception as e:
        loggers.error(f"Error in split_and_manage: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to split and manage tasks: {str(e)}")


async def generate_media_for_task_item(
    current_user: UserTenantDB,
    task_item: Dict[str, Any],
    task_item_id: ObjectId,
    generate_image_func,
    generate_audio_task_func
) -> Dict[str, Any]:
    """
    Generate media (image/audio) for a task item based on its type.

    Args:
        current_user: User context
        task_item: Task item data
        task_item_id: Task item ObjectId
        generate_image_func: Image generation function
        generate_audio_task_func: Audio generation function

    Returns:
        Updated media_files dict
    """
    try:
        media_files = {}
        question_type = task_item.get("question", {}).get("type", "")

        # Generate image for image_identification tasks
        if task_item.get("needs_image") and question_type == "image_identification":
            try:
                # Use answer_hint as keyword for image generation
                keyword = task_item.get("question", {}).get("answer_hint", "")
                if keyword:
                    loggers.info(f"Generating image for task {task_item_id} with keyword: {keyword}")
                    _file_text, file_info, usage_metadata = await generate_image_func(current_user, keyword)

                    if file_info:
                        media_files["image"] = {
                            "file_info": file_info,
                            "keyword": keyword,
                            "usage_metadata": usage_metadata
                        }
                        loggers.info(f"Generated image for task {task_item_id}")
            except Exception as e:
                loggers.error(f"Failed to generate image for task {task_item_id}: {e}")

        # Generate audio for speak_word and word_identification tasks
        if task_item.get("needs_audio") and question_type in ["speak_word", "word_identification"]:
            try:
                # Use answer_hint as keyword for audio generation
                keyword = task_item.get("question", {}).get("answer_hint", "")
                if keyword:
                    loggers.info(f"Generating audio for task {task_item_id} with keyword: {keyword}")
                    audio_data, usage_metadata = await generate_audio_task_func(keyword)

                    if audio_data:
                        # Store audio as base64
                        audio_base64 = base64.b64encode(audio_data).decode('utf-8')
                        media_files["audio"] = {
                            "data": audio_base64,
                            "keyword": keyword,
                            "usage_metadata": usage_metadata,
                            "format": "audio/wav"
                        }
                        loggers.info(f"Generated audio for task {task_item_id}")
            except Exception as e:
                loggers.error(f"Failed to generate audio for task {task_item_id}: {e}")

        return media_files

    except Exception as e:
        loggers.error(f"Error generating media for task {task_item_id}: {e}")
        return {}


async def generate_media_for_story_item(
    current_user: UserTenantDB,
    story_item: Dict[str, Any],
    story_item_id: ObjectId,
    generate_image_func,
    generate_audio_task_func
) -> Dict[str, Any]:
    """
    Generate media (image/audio) for a story item.

    Args:
        current_user: User context
        story_item: Story item data
        story_item_id: Story item ObjectId
        generate_image_func: Image generation function
        generate_audio_task_func: Audio generation function

    Returns:
        Updated media_files dict
    """
    try:
        media_files = {}

        # Generate image for story
        if story_item.get("needs_image"):
            try:
                image_description = story_item.get("image_description", "")
                if image_description:
                    loggers.info(f"Generating image for story {story_item_id} with description: {image_description}")
                    _file_text, file_info, usage_metadata = await generate_image_func(current_user, image_description)

                    if file_info:
                        media_files["image"] = {
                            "file_info": file_info,
                            "description": image_description,
                            "usage_metadata": usage_metadata
                        }
                        loggers.info(f"Generated image for story {story_item_id}")
            except Exception as e:
                loggers.error(f"Failed to generate image for story {story_item_id}: {e}")

        # Generate audio for story
        if story_item.get("needs_audio"):
            try:
                audio_keyword = story_item.get("audio_keyword", "")
                if audio_keyword:
                    loggers.info(f"Generating audio for story {story_item_id} with keyword: {audio_keyword}")
                    audio_data, usage_metadata = await generate_audio_task_func(audio_keyword)

                    if audio_data:
                        # Store audio as base64
                        audio_base64 = base64.b64encode(audio_data).decode('utf-8')
                        media_files["audio"] = {
                            "data": audio_base64,
                            "keyword": audio_keyword,
                            "usage_metadata": usage_metadata,
                            "format": "audio/wav"
                        }
                        loggers.info(f"Generated audio for story {story_item_id}")
            except Exception as e:
                loggers.error(f"Failed to generate audio for story {story_item_id}: {e}")

        return media_files

    except Exception as e:
        loggers.error(f"Error generating media for story {story_item_id}: {e}")
        return {}


async def save_task_set_to_db(
    current_user: UserTenantDB,
    task_set: Dict[str, Any],
    content_set_id: ObjectId,
    story_script: str,
    audio_data: bytes,
    story_prompt: str
) -> ObjectId:
    """
    Save task set to curated_content_set collection.

    Args:
        current_user: User context
        task_set: Task set data
        content_set_id: Content set ID
        story_script: Generated story script
        audio_data: Generated audio data
        story_prompt: Original story prompt

    Returns:
        ObjectId of saved task set
    """
    try:
        # Create the content set document
        content_set_doc = {
            "_id": content_set_id,
            "title": task_set["title"],
            "theme_id": None,  # Will be set later if theme is specified
            "difficulty_level": task_set["difficulty_level"],
            "status": "completed",
            "gentype": task_set["gentype"],
            "story_prompt": story_prompt,
            "story_script": story_script,
            "audio_data": base64.b64encode(audio_data).decode('utf-8'),  # Store as base64
            "tasks_count": task_set["tasks_count"],
            "metadata": task_set["metadata"],
            "created_by": current_user.user.id,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc)
        }

        # Insert the content set
        await current_user.async_db.curated_content_set.insert_one(content_set_doc)
        loggers.info(f"Saved curated content set with ID: {content_set_id}")

        return content_set_id

    except Exception as e:
        loggers.error(f"Error saving task set to database: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to save task set: {str(e)}")


async def save_task_items_to_db(
    current_user: UserTenantDB,
    task_items: List[Dict[str, Any]],
    content_set_id: ObjectId,
    generate_image_func,
    generate_audio_task_func
) -> List[ObjectId]:
    """
    Save task items to curated_content_items collection with media generation.

    Args:
        current_user: User context
        task_items: List of task item data
        content_set_id: Content set ID
        generate_image_func: Image generation function
        generate_audio_task_func: Audio generation function

    Returns:
        List of ObjectIds of saved task items
    """
    try:
        saved_task_ids = []

        for task_item in task_items:
            task_item_id = ObjectId()

            # Generate media if needed
            media_files = await generate_media_for_task_item(
                current_user, task_item, task_item_id,
                generate_image_func, generate_audio_task_func
            )

            # Create task item document
            task_item_doc = {
                "_id": task_item_id,
                "content_set_id": content_set_id,
                "task_index": task_item["task_index"],
                "question": task_item["question"],
                "max_score": task_item["max_score"],
                "complexity": task_item["complexity"],
                "media_files": media_files,
                "media_generated": bool(media_files),
                "created_by": current_user.user.id,
                "created_at": datetime.now(timezone.utc)
            }

            # Insert task item
            await current_user.async_db.curated_content_items.insert_one(task_item_doc)
            saved_task_ids.append(task_item_id)
            loggers.info(f"Saved task item {task_item['task_index']} with ID: {task_item_id}")

        loggers.info(f"Saved {len(saved_task_ids)} task items")
        return saved_task_ids

    except Exception as e:
        loggers.error(f"Error saving task items to database: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to save task items: {str(e)}")


async def save_story_items_to_db(
    current_user: UserTenantDB,
    story_items: List[Dict[str, Any]],
    content_set_id: ObjectId,
    generate_image_func,
    generate_audio_task_func
) -> List[ObjectId]:
    """
    Save story items to curated_content_items collection with media generation.

    Args:
        current_user: User context
        story_items: List of story item data
        content_set_id: Content set ID
        generate_image_func: Image generation function
        generate_audio_task_func: Audio generation function

    Returns:
        List of ObjectIds of saved story items
    """
    try:
        saved_story_ids = []

        for story_item in story_items:
            story_item_id = ObjectId()

            # Generate media if needed
            media_files = await generate_media_for_story_item(
                current_user, story_item, story_item_id,
                generate_image_func, generate_audio_task_func
            )

            # Create story item document
            story_item_doc = {
                "_id": story_item_id,
                "content_set_id": content_set_id,
                "story_index": story_item["story_index"],
                "stage": story_item["stage"],
                "thumbnail": story_item["thumbnail"],
                "script": story_item["script"],
                "image_description": story_item["image_description"],
                "audio_keyword": story_item["audio_keyword"],
                "media_files": media_files,
                "media_generated": bool(media_files),
                "item_type": "story",  # Distinguish from task items
                "created_by": current_user.user.id,
                "created_at": datetime.now(timezone.utc)
            }

            # Insert story item
            await current_user.async_db.curated_content_items.insert_one(story_item_doc)
            saved_story_ids.append(story_item_id)
            loggers.info(f"Saved story item {story_item['story_index']} with ID: {story_item_id}")

        loggers.info(f"Saved {len(saved_story_ids)} story items")
        return saved_story_ids

    except Exception as e:
        loggers.error(f"Error saving story items to database: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to save story items: {str(e)}")