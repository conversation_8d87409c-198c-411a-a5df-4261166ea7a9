"""
Helper functions for splitting and managing curated content generation.

This module provides utilities to split generated tasks into proper database structures
and manage media generation for task items and story items.
"""

from datetime import datetime, timezone
from bson import ObjectId
from typing import Dict, Any, List, Tuple
import base64
from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from fastapi import HTTPException

loggers = setup_new_logging(__name__)


async def split_and_manage(generated_tasks: Dict[str, Any]) -> Tuple[Dict[str, Any], List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    Split generated tasks into task_set, task_items, and story items following v2 audio process structure.

    Args:
        generated_tasks: Output from generate_tasks function

    Returns:
        Tuple of (task_set_dict, task_items_list, story_items_list)
    """
    try:
        loggers.info("Starting split_and_manage for curated content")

        # Extract basic information
        title = generated_tasks.get("title", "Generated Curated Content")
        tasks = generated_tasks.get("tasks", [])
        metadata = generated_tasks.get("metadata", {})

        if not tasks:
            raise ValueError("No tasks found in generated_tasks")

        # Separate text tasks from media tasks (following v2 pattern)
        text_tasks = []
        media_tasks = []

        for task in tasks:
            question_data = task.get("question", {})
            task_type = question_data.get("type", "single_choice")

            if task_type in ["speak_word", "word_identification", "image_identification"]:
                media_tasks.append(task)
            else:
                text_tasks.append(task)

        # Create task set structure (following v2 task_sets collection structure)
        task_set = {
            "title": title,
            "input_type": "text",  # Curated content starts from text prompt
            "tasks": [],  # Will be populated with task IDs after insertion
            "stories": [],  # Will be populated with story IDs after insertion
            "total_tasks": len(tasks),
            "total_stories": len(tasks),  # Each task has a story
            "text_tasks_ready": len(text_tasks),
            "media_tasks_pending": len(media_tasks),
            "attempted_tasks": 0,
            "total_verified": 0,
            "status": "pending",
            "gentype": "primary",
            "has_follow_up": False,
            "total_score": 0,
            "scored": 0,
            "attempts_count": 0,
            "metadata": metadata
        }

        # Create task items (following v2 task_items collection structure)
        task_items = []
        for i, task in enumerate(tasks):
            question_data = task.get("question", {})
            task_type = question_data.get("type", "single_choice")

            # Create task item following exact v2 structure
            task_item = {
                "type": task_type,
                "title": task.get("title", f"Task {i + 1}"),
                "question": {
                    "type": task_type,
                    "text": question_data.get("text", ""),
                    "translated_text": question_data.get("translated_text", ""),
                    "options": question_data.get("options", {}),
                    "answer_hint": question_data.get("answer_hint", ""),
                    "metadata": {}  # Will be populated with media info later
                },
                "correct_answer": {
                    "type": task_type,
                    "value": question_data.get("answer", "")
                },
                "user_answer": None,
                "status": "pending",
                "result": None,
                "remark": None,
                "total_score": task.get("max_score", 10),
                "scored": 0,
                "submitted": False,
                "submitted_at": None,
                "attempts_count": 0,
                "difficulty_level": task.get("complexity", 1),
                "metadata": {
                    "_media_ready": False,
                    "_priority": "media_pending" if task_type in ["speak_word", "word_identification", "image_identification"] else "text_ready"
                }
            }
            task_items.append(task_item)

        # Create story items (following v2 story_steps collection structure)
        story_items = []
        for i, task in enumerate(tasks):
            story_data = task.get("story", {})

            # Create story item following exact v2 structure
            story_item = {
                "stage": i + 1,
                "script": story_data.get("script", ""),
                "image": story_data.get("image", ""),  # Image description
                "thumbnail": story_data.get("thumbnail", "📖"),
                "audio_metadata": {
                    "_audio_ready": False,
                    "_priority": "audio_pending"
                },
                "image_metadata": {
                    "_image_ready": False,
                    "_priority": "image_pending"
                }
            }
            story_items.append(story_item)

        loggers.info(f"Split complete: {len(task_items)} task items ({len(text_tasks)} text, {len(media_tasks)} media), {len(story_items)} story items")
        return task_set, task_items, story_items

    except Exception as e:
        loggers.error(f"Error in split_and_manage: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to split and manage tasks: {str(e)}")


# Old media generation functions removed - now using v2 pattern with background generation


# Old story media generation function removed - now using v2 pattern with background generation


async def save_task_set_to_db(
    current_user: UserTenantDB,
    task_set: Dict[str, Any],
    task_set_id: ObjectId,
    story_script: str,
    audio_data: bytes,
    story_prompt: str,
    audio_storage_info: Dict[str, Any] = None
) -> ObjectId:
    """
    Save task set to curated_content_set collection following v2 pattern.

    Args:
        current_user: User context
        task_set: Task set data
        task_set_id: Task set ID
        story_script: Generated story script
        audio_data: Generated audio data
        story_prompt: Original story prompt

    Returns:
        ObjectId of saved task set
    """
    try:
        # Create input_content following v2 pattern with both script and audio info
        input_content = {
            "type": "curated_content",
            "original_prompt": story_prompt,
            "generated_script": story_script,
            "script_length": len(story_script),
            "audio_length_bytes": len(audio_data) if audio_data else 0,
            "content_type": "text/plain",
            "created_at": datetime.now(timezone.utc).isoformat()
        }

        # Add audio storage information if provided (following v2 pattern)
        if audio_storage_info:
            input_content.update({
                "audio_metadata": {
                    "object_name": audio_storage_info.get("object_name"),
                    "bucket_name": audio_storage_info.get("bucket_name"),
                    "object_path": audio_storage_info.get("object_path"),
                    "file_name": audio_storage_info.get("file_name"),
                    "content_type": audio_storage_info.get("content_type", "audio/wav"),
                    "size_bytes": audio_storage_info.get("size_bytes"),
                    "folder": audio_storage_info.get("folder", "curated_audio"),
                    "file_extension": audio_storage_info.get("file_extension", ".wav"),
                    "url": audio_storage_info.get("url")
                }
            })

        # Create the content set document following v2 task_sets structure
        content_set_doc = {
            "_id": task_set_id,
            "user_id": ObjectId(current_user.user.id),
            "title": task_set["title"],
            "input_type": task_set["input_type"],
            "input_content": input_content,  # Now contains both script and audio info
            "tasks": task_set["tasks"],  # Will be updated with task IDs
            "stories": task_set["stories"],  # Will be updated with story IDs
            "total_tasks": task_set["total_tasks"],
            "total_stories": task_set["total_stories"],
            "text_tasks_ready": task_set["text_tasks_ready"],
            "media_tasks_pending": task_set["media_tasks_pending"],
            "attempted_tasks": task_set["attempted_tasks"],
            "total_verified": task_set["total_verified"],
            "status": task_set["status"],
            "gentype": task_set["gentype"],
            "has_follow_up": task_set["has_follow_up"],
            "total_score": task_set["total_score"],
            "scored": task_set["scored"],
            "attempts_count": task_set["attempts_count"],
            "metadata": task_set["metadata"],
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc)
        }

        # Insert the content set
        await current_user.async_db.curated_content_set.insert_one(content_set_doc)
        loggers.info(f"Saved curated content set with ID: {task_set_id}")

        return task_set_id

    except Exception as e:
        loggers.error(f"Error saving task set to database: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to save task set: {str(e)}")


async def save_task_items_to_db(
    current_user: UserTenantDB,
    task_items: List[Dict[str, Any]],
    task_set_id: ObjectId
) -> List[ObjectId]:
    """
    Save task items to curated_content_items collection following v2 pattern.
    Media generation happens AFTER insertion, not during.

    Args:
        current_user: User context
        task_items: List of task item data
        task_set_id: Content set ID

    Returns:
        List of ObjectIds of saved task items
    """
    try:
        saved_task_ids = []

        for task_item in task_items:
            task_item_id = ObjectId()
            # Create task item document following exact v2 task_items structure
            task_item_doc = {
                "_id": task_item_id,
                "content_set_id": task_set_id,  # Link to content set
                "type": task_item["type"],
                "title": task_item["title"],
                "question": task_item["question"],
                "correct_answer": task_item["correct_answer"],
                "user_answer": task_item["user_answer"],
                "status": task_item["status"],
                "result": task_item["result"],
                "remark": task_item["remark"],
                "total_score": task_item["total_score"],
                "scored": task_item["scored"],
                "submitted": task_item["submitted"],
                "submitted_at": task_item["submitted_at"],
                "attempts_count": task_item["attempts_count"],
                "difficulty_level": task_item["difficulty_level"],
                "metadata": task_item["metadata"],
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            }

            # Insert task item
            await current_user.async_db.curated_content_items.insert_one(task_item_doc)
            saved_task_ids.append(task_item_id)
            loggers.info(f"Saved task item {task_item['title']} with ID: {task_item_id}")

        loggers.info(f"Saved {len(saved_task_ids)} task items")
        return saved_task_ids

    except Exception as e:
        loggers.error(f"Error saving task items to database: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to save task items: {str(e)}")


async def save_story_items_to_db(
    current_user: UserTenantDB,
    story_items: List[Dict[str, Any]],
    task_set_id: ObjectId
) -> List[ObjectId]:
    """
    Save story items to curated_content_items collection following v2 pattern.
    Media generation happens AFTER insertion, not during.

    Args:
        current_user: User context
        story_items: List of story item data
        content_set_id: Content set ID

    Returns:
        List of ObjectIds of saved story items
    """
    try:
        saved_story_ids = []

        for story_item in story_items:
            story_item_id = ObjectId()

            # Create story item document following exact v2 story_steps structure
            story_item_doc = {
                "_id": story_item_id,
                "user_id": str(current_user.user.id),
                "stage": story_item["stage"],
                "script": story_item["script"],
                "image": story_item["image"],
                "thumbnail": story_item["thumbnail"],
                "audio_metadata": story_item["audio_metadata"],
                "image_metadata": story_item["image_metadata"],
                "content_set_id": task_set_id,  # Link to content set
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            }

            # Insert story item
            await current_user.async_db.curated_content_items.insert_one(story_item_doc)
            saved_story_ids.append(story_item_id)
            loggers.info(f"Saved story item stage {story_item['stage']} with ID: {story_item_id}")

        loggers.info(f"Saved {len(saved_story_ids)} story items")
        return saved_story_ids

    except Exception as e:
        loggers.error(f"Error saving story items to database: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to save story items: {str(e)}")


async def generate_media_for_tasks_and_stories(
    current_user: UserTenantDB,
    task_item_ids: List[ObjectId],
    story_item_ids: List[ObjectId],
    generate_image_func,
    generate_audio_task_func
):
    """
    Generate media for task items and story items AFTER they are saved to database.
    This follows the v2 pattern where items are inserted first, then media is generated.

    Args:
        current_user: User context
        task_item_ids: List of task item ObjectIds
        story_item_ids: List of story item ObjectIds
        generate_image_func: Image generation function
        generate_audio_task_func: Audio generation function
    """
    try:
        loggers.info(f"Starting background media generation for {len(task_item_ids)} tasks and {len(story_item_ids)} stories")

        # Generate media for task items
        for task_id in task_item_ids:
            try:
                # Get task item from database
                task_item = await current_user.async_db.curated_content_items.find_one({"_id": task_id})
                if not task_item:
                    continue

                task_type = task_item.get("type", "")
                question = task_item.get("question", {})

                # Generate image for image_identification tasks
                if task_type == "image_identification":
                    keyword = question.get("answer_hint", "")
                    if keyword:
                        loggers.info(f"Generating image for task {task_id} with keyword: {keyword}")
                        _, file_info, usage_metadata = await generate_image_func(current_user, keyword)

                        if file_info:
                            # Update task item with image metadata
                            await current_user.async_db.curated_content_items.update_one(
                                {"_id": task_id},
                                {
                                    "$set": {
                                        "question.metadata": file_info,
                                        "metadata._media_ready": True,
                                        "metadata._priority": "image_ready",
                                        "updated_at": datetime.now(timezone.utc)
                                    }
                                }
                            )
                            loggers.info(f"✅ Image generated for task {task_id}")

                # Generate audio for speak_word and word_identification tasks
                elif task_type in ["speak_word", "word_identification"]:
                    keyword = question.get("answer_hint", "")
                    if keyword:
                        loggers.info(f"Generating audio for task {task_id} with keyword: {keyword}")
                        audio_data, usage_metadata = await generate_audio_task_func(keyword)

                        if audio_data:
                            # Store audio as base64 in metadata
                            audio_base64 = base64.b64encode(audio_data).decode('utf-8')
                            audio_metadata = {
                                "data": audio_base64,
                                "keyword": keyword,
                                "format": "audio/wav",
                                "usage": usage_metadata
                            }

                            # Update task item with audio metadata
                            await current_user.async_db.curated_content_items.update_one(
                                {"_id": task_id},
                                {
                                    "$set": {
                                        "question.metadata": audio_metadata,
                                        "metadata._media_ready": True,
                                        "metadata._priority": "audio_ready",
                                        "updated_at": datetime.now(timezone.utc)
                                    }
                                }
                            )
                            loggers.info(f"✅ Audio generated for task {task_id}")

            except Exception as e:
                loggers.error(f"Failed to generate media for task {task_id}: {e}")

        # Generate media for story items
        for story_id in story_item_ids:
            try:
                # Get story item from database
                story_item = await current_user.async_db.curated_content_items.find_one({"_id": story_id})
                if not story_item:
                    continue

                # Generate image for story
                image_description = story_item.get("image", "")
                if image_description:
                    loggers.info(f"Generating image for story {story_id} with description: {image_description}")
                    _, file_info, usage_metadata = await generate_image_func(current_user, image_description)

                    if file_info:
                        # Update story item with image metadata
                        await current_user.async_db.curated_content_items.update_one(
                            {"_id": story_id},
                            {
                                "$set": {
                                    "image_metadata": {
                                        **file_info,
                                        "_image_ready": True,
                                        "_priority": "image_ready",
                                        "usage": usage_metadata,
                                        "generated_at": datetime.now(timezone.utc)
                                    },
                                    "updated_at": datetime.now(timezone.utc)
                                }
                            }
                        )
                        loggers.info(f"✅ Image generated for story {story_id}")

                # Generate audio for story
                script_text = story_item.get("script", "")
                if script_text:
                    loggers.info(f"Generating audio for story {story_id} with script: {script_text[:50]}...")
                    audio_data, usage_metadata = await generate_audio_task_func(script_text)

                    if audio_data:
                        # Store audio as base64
                        audio_base64 = base64.b64encode(audio_data).decode('utf-8')
                        audio_metadata = {
                            "data": audio_base64,
                            "script": script_text,
                            "format": "audio/wav",
                            "_audio_ready": True,
                            "_priority": "audio_ready",
                            "usage": usage_metadata,
                            "generated_at": datetime.now(timezone.utc)
                        }

                        # Update story item with audio metadata
                        await current_user.async_db.curated_content_items.update_one(
                            {"_id": story_id},
                            {
                                "$set": {
                                    "audio_metadata": audio_metadata,
                                    "updated_at": datetime.now(timezone.utc)
                                }
                            }
                        )
                        loggers.info(f"✅ Audio generated for story {story_id}")

            except Exception as e:
                loggers.error(f"Failed to generate media for story {story_id}: {e}")

        loggers.info("✅ Background media generation completed")

    except Exception as e:
        loggers.error(f"Error in background media generation: {e}")