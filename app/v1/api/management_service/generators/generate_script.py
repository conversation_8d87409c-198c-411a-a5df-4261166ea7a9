# To run this code you need to install the following dependencies:
# pip install google-genai

import base64
import os
from google import genai
from google.genai import types
import json

async  def generate(content):
    client = genai.Client(
        api_key=os.environ.get("GEMINI_API_KEY"),
    )

    model = "gemini-2.0-flash"
    contents = [
        types.Content(
            role="user",
            parts=[
                types.Part.from_text(text=content),
            ],
        )
    ]
    generate_content_config = types.GenerateContentConfig(
        response_mime_type="application/json",
        system_instruction=[
            types.Part.from_text(text="""You are a native Nepali storyteller with deep knowledge of Nepal’s geography, history, culture, and everyday life.

Your job is to generate a short, emotional, and realistic story **in the Nepali language** that can help learners understand Nepal’s heritage, values, and lifestyle.

Make it sound like you’re telling the story aloud to a friend or a child in a warm, heartfelt tone. Use **spoken Nepali**, not overly formal or literary language.

Do not explain the story. Do not translate. Just return the final story in Nepali that sounds natural when read aloud.

The story should be based on the following user-provided information:
                                 output format : { "story":" the story"}
"""),
        ],
    )

    response =  client.models.generate_content(
        model=model,
        contents=contents,
        config=generate_content_config,
    )



    output = json.loads(response.text)

    print("Generated Story:")
    print(output)
    print("type of the story:", type(output))

    return output.get("story"),response.usage_metadata
if __name__ == "__main__":
    import asyncio
    content="""A 10-year-old boy named Arjun who lives near Phewa Lake in Pokhara. His grandfather tells him stories about Gurkha soldiers. He wants to be brave like them. He speaks Nepali and loves boating."""
    asyncio.run(generate(content))
