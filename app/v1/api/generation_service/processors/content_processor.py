"""
Content Processor for Generation Service

This module contains the main processing logic for curated content generation.
It uses the existing creator and generators that were moved from management service.
"""

from datetime import datetime, timezone
from bson import ObjectId
from typing import Dict, Any

from app.shared.utils.logger import setup_new_logging
from app.shared.models.user import UserTenantDB
from app.shared.db_enums import CollectionName
from app.shared.models.curated_content import CuratedContentRequest

# Import the moved generators and creator
from ..generators.generate_script import generate as generate_content
from ..generators.audiogen import generate as generate_audio
from ..generators.task_gen import generate as generate_tasks
from ..generators.tasks.task_image import generate as generate_image
from ..generators.tasks.task_audio import generate as generate_audio_task
from ..creator.split_and_manage import split_and_manage

loggers = setup_new_logging(__name__)


async def process_curated_content_task(
    current_user: UserTenantDB,
    request: CuratedContentRequest,
    task_id: ObjectId
):
    """
    Process a curated content generation task.
    This is the main processing function moved from management service.
    
    Args:
        current_user: User database connection
        request: Content generation request
        task_id: Task ID that was stored in editor_prompts
    """
    try:
        # Update status to 'generation'
        await current_user.async_db[CollectionName.editor_prompts].update_one(
            {"task_set_id": task_id},
            {"$set": {"status": "generation", "updated_at": datetime.now(timezone.utc)}}
        )
        loggers.info(f"🔄 Started generation for task_id: {task_id}")

        # Use the same task_id as task_set_id
        task_set_id = task_id

        # step1 generate script
        generated_script, _usage_metadata = await generate_content(request.content)
        loggers.info(f"Generated script: {generated_script}")

        # step2 generate audio
        generated_audio, _audio_usage_metadata = await generate_audio(generated_script)
        loggers.info(f"Generated audio bytes length: {len(generated_audio) if generated_audio else 0}")

        # Save generated audio to MinIO (following v2 pattern)
        audio_storage_info = None
        if generated_audio and current_user.minio:
            try:
                from app.shared.async_minio_client import create_async_minio_client
                import hashlib

                async_minio_client = create_async_minio_client(current_user.minio)

                # Create a unique filename for the curated content audio
                content_hash = hashlib.sha256(request.content.encode()).hexdigest()[:10]
                custom_filename = f"curated_{content_hash}_audio.wav"

                audio_storage_info = await async_minio_client.save_file_async(
                    data=generated_audio,
                    user_id=current_user.user.id,
                    content_type="audio/wav",
                    folder="curated_audio",
                    file_extension=".wav",
                    custom_filename=custom_filename,
                )
                loggers.info(f"📁 Curated audio stored in MinIO: {audio_storage_info.get('object_path')}")
            except Exception as e:
                loggers.error(f"❌ Error storing curated audio in MinIO: {e}")
                audio_storage_info = {}

        # step3 generate tasks
        generated_tasks, _tasks_usage_metadata = await generate_tasks(generated_audio)
        loggers.info(f"Generated tasks: {generated_tasks}")

        # Validate generated tasks structure
        if not isinstance(generated_tasks, dict):
            raise ValueError(f"Generated tasks should be a dict, got {type(generated_tasks)}")

        if "error" in generated_tasks:
            raise ValueError(f"Task generation error: {generated_tasks['error']}")

        if not generated_tasks.get("tasks"):
            raise ValueError("No tasks found in generated content")

        # step4 split properly make three coll wise datast task_sets, task_items, and story
        task_set, task_items, story_items = await split_and_manage(generated_tasks)
        loggers.info(f"Split complete: task_set with {len(task_items)} task items, {len(story_items)} story items")

        # step5 save to database following v2 pattern: insert first, then generate media
        await save_content_to_database(
            current_user, task_set, task_items, story_items, task_set_id,
            generated_script, generated_audio, request.content, audio_storage_info
        )

        # Update status to 'completed'
        await current_user.async_db[CollectionName.editor_prompts].update_one(
            {"task_set_id": task_id},
            {"$set": {
                "status": "completed",
                "task_count": len(task_items),
                "story_count": len(story_items),
                "updated_at": datetime.now(timezone.utc)
            }}
        )

        loggers.info(f"✅ Completed processing for task_id: {task_id}: task_set_id={task_set_id}")

    except Exception as e:
        loggers.error(f"❌ Background processing failed for task_id: {task_id}: {e}")
        # Update status to 'failed'
        try:
            await current_user.async_db[CollectionName.editor_prompts].update_one(
                {"task_set_id": task_id},
                {"$set": {
                    "status": "failed",
                    "error": str(e),
                    "updated_at": datetime.now(timezone.utc)
                }}
            )
        except Exception as update_error:
            loggers.error(f"Failed to update error status: {update_error}")


async def save_content_to_database(
    current_user: UserTenantDB,
    task_set: Dict[str, Any],
    task_items: list,
    story_items: list,
    task_set_id: ObjectId,
    generated_script: str,
    generated_audio: bytes,
    original_content: str,
    audio_storage_info: Dict[str, Any]
):
    """Save generated content to database following v2 pattern."""
    # This function would contain the database saving logic
    # that was in the original creator/__init__.py
    
    # Generate ObjectIds for task set and items
    task_item_ids = [ObjectId() for _ in task_items]
    story_item_ids = [ObjectId() for _ in story_items]

    # TODO: Implement the actual database saving logic here
    # This would include:
    # 1. Save task items and story items to database WITHOUT media
    # 2. Update task_set with the actual task and story IDs
    # 3. Save task set with references to task and story IDs
    # 4. Generate media for task items and story items
    
    loggers.info(f"📝 Saved to database: task_set_id={task_set_id}, task_items={len(task_item_ids)}, story_items={len(story_item_ids)}")
    loggers.info("🎬 Media generation completed")
