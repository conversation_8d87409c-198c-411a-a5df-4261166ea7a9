"""
Generation Service - Standalone Content Processor

This service runs independently and processes curated content generation tasks.
It connects to the database directly and processes tasks without HTTP routes.
"""

import asyncio
import os
import signal
import sys
from datetime import datetime, timezone
from bson import ObjectId

from app.shared.utils.logger import setup_new_logging
from app.shared.models.user import UserTenantDB
from app.shared.db_enums import CollectionName
from app.shared.models.curated_content import CuratedContentRequest

from .processors.content_processor import process_curated_content_task

loggers = setup_new_logging(__name__)


class GenerationService:
    """Standalone generation service that processes content tasks."""
    
    def __init__(self):
        self.running = False
        self.poll_interval = int(os.getenv("GENERATION_POLL_INTERVAL", 5))  # seconds
        
    async def start(self):
        """Start the generation service."""
        self.running = True
        loggers.info("🚀 Generation Service started - polling for tasks...")
        
        try:
            while self.running:
                await self.process_pending_tasks()
                await asyncio.sleep(self.poll_interval)
        except Exception as e:
            loggers.error(f"❌ Generation Service error: {e}")
        finally:
            loggers.info("🛑 Generation Service stopped")
    
    async def stop(self):
        """Stop the generation service."""
        self.running = False
        loggers.info("🛑 Stopping Generation Service...")
    
    async def process_pending_tasks(self):
        """Check for pending tasks and process them."""
        try:
            # This would need to be implemented with proper database connection
            # For now, this is the structure
            loggers.debug("🔍 Checking for pending generation tasks...")
            
            # TODO: Connect to database and find pending tasks
            # pending_tasks = await db.find({"status": "pending", "type": "curated_content"})
            
            # for task in pending_tasks:
            #     await self.process_single_task(task)
            
        except Exception as e:
            loggers.error(f"❌ Error processing pending tasks: {e}")
    
    async def process_single_task(self, task_data: dict):
        """Process a single generation task."""
        try:
            task_id = task_data.get("task_set_id")
            content = task_data.get("content")
            user_id = task_data.get("user_id")
            
            loggers.info(f"🔄 Processing task {task_id} for user {user_id}")
            
            # Create request object
            request = CuratedContentRequest(content=content)
            
            # TODO: Create UserTenantDB object with proper database connection
            # current_user = UserTenantDB(...)
            
            # Process the content
            # await process_curated_content_task(current_user, request, ObjectId(task_id))
            
            loggers.info(f"✅ Completed task {task_id}")
            
        except Exception as e:
            loggers.error(f"❌ Error processing task {task_data.get('task_set_id', 'unknown')}: {e}")


# Global service instance
service = GenerationService()


def signal_handler(signum, frame):
    """Handle shutdown signals."""
    loggers.info(f"Received signal {signum}, shutting down...")
    asyncio.create_task(service.stop())


async def main():
    """Main entry point for the generation service."""
    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        await service.start()
    except KeyboardInterrupt:
        loggers.info("Received keyboard interrupt")
    finally:
        await service.stop()


if __name__ == "__main__":
    loggers.info("Starting Generation Service...")
    asyncio.run(main())
