"""
Generation Service Configuration

Configuration settings for the standalone generation service.
"""

import os
from typing import Optional


class GenerationServiceConfig:
    """Configuration class for generation service."""
    
    # Service settings
    POLL_INTERVAL: int = int(os.getenv("GENERATION_POLL_INTERVAL", 5))  # seconds
    MAX_CONCURRENT_TASKS: int = int(os.getenv("GENERATION_MAX_CONCURRENT", 3))
    
    # Database settings (these would be inherited from main app config)
    MONGODB_URL: Optional[str] = os.getenv("MONGODB_URL")
    DATABASE_NAME: Optional[str] = os.getenv("DATABASE_NAME")
    
    # API Keys
    GEMINI_API_KEY: Optional[str] = os.getenv("GEMINI_API_KEY")
    
    # MinIO settings
    MINIO_ENDPOINT: Optional[str] = os.getenv("MINIO_ENDPOINT")
    MINIO_ACCESS_KEY: Optional[str] = os.getenv("MINIO_ACCESS_KEY")
    MINIO_SECRET_KEY: Optional[str] = os.getenv("MINIO_SECRET_KEY")
    
    # Logging
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    
    @classmethod
    def validate(cls) -> bool:
        """Validate that required configuration is present."""
        required_vars = [
            "MONGODB_URL",
            "DATABASE_NAME", 
            "GEMINI_API_KEY"
        ]
        
        missing = []
        for var in required_vars:
            if not getattr(cls, var):
                missing.append(var)
        
        if missing:
            print(f"❌ Missing required environment variables: {', '.join(missing)}")
            return False
        
        return True


# Global config instance
config = GenerationServiceConfig()
