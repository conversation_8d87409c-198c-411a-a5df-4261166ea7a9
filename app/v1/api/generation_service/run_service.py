#!/usr/bin/env python3
"""
Generation Service Runner

Simple script to run the generation service as a standalone process.
This service polls the database for pending tasks and processes them.
"""

import asyncio
import os
import sys

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../..'))

from app.v1.api.generation_service.main import main

if __name__ == "__main__":
    print("🚀 Starting Generation Service...")
    print("Press Ctrl+C to stop")
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Generation Service stopped by user")
    except Exception as e:
        print(f"❌ Generation Service failed: {e}")
        sys.exit(1)
